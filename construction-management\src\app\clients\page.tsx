'use client';

import React, { useState } from 'react';
import { Layout } from '@/components/layout/Layout';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { mockClients, mockProjects } from '@/lib/data';
import { Client } from '@/types';

export default function ClientsPage() {
  const [clients] = useState<Client[]>(mockClients);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  const filteredClients = clients.filter(client => {
    const matchesSearch = client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.phone.includes(searchTerm);
    const matchesType = typeFilter === 'all' || client.clientType === typeFilter;
    return matchesSearch && matchesType;
  });

  const getClientTypeText = (type: string) => {
    return type === 'individual' ? 'فرد' : 'شركة';
  };

  const getClientProjects = (clientId: string) => {
    return mockProjects.filter(project => project.clientId === clientId);
  };

  const getActiveProjectsCount = (clientId: string) => {
    const projects = getClientProjects(clientId);
    return projects.filter(project => project.status === 'in-progress').length;
  };

  const getTotalProjectsValue = (clientId: string) => {
    const projects = getClientProjects(clientId);
    return projects.reduce((total, project) => total + project.estimatedCost, 0);
  };

  return (
    <Layout title="إدارة العملاء">
      <div className="space-y-6">
        {/* شريط البحث والفلاتر */}
        <Card>
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex flex-col md:flex-row gap-4 flex-1">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="البحث في العملاء..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">جميع الأنواع</option>
                <option value="individual">أفراد</option>
                <option value="company">شركات</option>
              </select>
            </div>
            <Button>
              إضافة عميل جديد
            </Button>
          </div>
        </Card>

        {/* قائمة العملاء */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredClients.map((client) => (
            <Card key={client.id} className="hover:shadow-lg transition-shadow">
              <div className="space-y-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center ml-3">
                      <span className="text-white text-lg font-bold">
                        {client.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{client.name}</h3>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        client.clientType === 'individual' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {getClientTypeText(client.clientType)}
                      </span>
                    </div>
                  </div>
                </div>

                {client.company && (
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-sm font-medium text-gray-900">{client.company}</p>
                  </div>
                )}

                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <span className="text-gray-600 w-16">البريد:</span>
                    <span className="font-medium">{client.email}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <span className="text-gray-600 w-16">الهاتف:</span>
                    <span className="font-medium">{client.phone}</span>
                  </div>
                  <div className="flex items-start text-sm">
                    <span className="text-gray-600 w-16">العنوان:</span>
                    <span className="font-medium">{client.address}</span>
                  </div>
                </div>

                {/* إحصائيات العميل */}
                <div className="grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
                  <div className="text-center">
                    <div className="text-lg font-bold text-blue-600">
                      {getClientProjects(client.id).length}
                    </div>
                    <div className="text-xs text-gray-600">إجمالي المشاريع</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600">
                      {getActiveProjectsCount(client.id)}
                    </div>
                    <div className="text-xs text-gray-600">مشاريع نشطة</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-purple-600">
                      {(getTotalProjectsValue(client.id) / 1000).toFixed(0)}ك
                    </div>
                    <div className="text-xs text-gray-600">قيمة المشاريع</div>
                  </div>
                </div>

                {/* تاريخ الإضافة */}
                <div className="text-xs text-gray-500">
                  عميل منذ: {client.createdAt.toLocaleDateString('ar-SA')}
                </div>

                {/* أزرار الإجراءات */}
                <div className="flex gap-2 pt-4 border-t border-gray-200">
                  <Button size="sm" className="flex-1">
                    عرض التفاصيل
                  </Button>
                  <Button variant="outline" size="sm">
                    تعديل
                  </Button>
                  <Button variant="outline" size="sm">
                    📞
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {filteredClients.length === 0 && (
          <Card>
            <div className="text-center py-12">
              <div className="text-6xl mb-4">👥</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا يوجد عملاء</h3>
              <p className="text-gray-600 mb-4">لم يتم العثور على عملاء يطابقون معايير البحث</p>
              <Button>
                إضافة عميل جديد
              </Button>
            </div>
          </Card>
        )}
      </div>
    </Layout>
  );
}
