'use client';

import React, { useState } from 'react';
import { Layout } from '@/components/layout/Layout';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { mockEmployees, mockProjects } from '@/lib/data';
import { Employee } from '@/types';

export default function EmployeesPage() {
  const [employees] = useState<Employee[]>(mockEmployees);
  const [searchTerm, setSearchTerm] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         employee.position.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDepartment = departmentFilter === 'all' || employee.department === departmentFilter;
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && employee.isActive) ||
                         (statusFilter === 'inactive' && !employee.isActive);
    return matchesSearch && matchesDepartment && matchesStatus;
  });

  const getDepartmentText = (department: string) => {
    switch (department) {
      case 'management': return 'الإدارة';
      case 'engineering': return 'الهندسة';
      case 'construction': return 'البناء';
      case 'finance': return 'المالية';
      case 'hr': return 'الموارد البشرية';
      default: return department;
    }
  };

  const getDepartmentColor = (department: string) => {
    switch (department) {
      case 'management': return 'bg-purple-100 text-purple-800';
      case 'engineering': return 'bg-blue-100 text-blue-800';
      case 'construction': return 'bg-orange-100 text-orange-800';
      case 'finance': return 'bg-green-100 text-green-800';
      case 'hr': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getEmployeeProjects = (employeeId: string) => {
    return mockProjects.filter(project => 
      mockEmployees.find(emp => emp.id === employeeId)?.assignedProjects.includes(project.id)
    );
  };

  const getActiveProjectsCount = (employeeId: string) => {
    const projects = getEmployeeProjects(employeeId);
    return projects.filter(project => project.status === 'in-progress').length;
  };

  const getYearsOfService = (hireDate: Date) => {
    const years = new Date().getFullYear() - hireDate.getFullYear();
    return years;
  };

  return (
    <Layout title="إدارة الموظفين">
      <div className="space-y-6">
        {/* شريط البحث والفلاتر */}
        <Card>
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex flex-col md:flex-row gap-4 flex-1">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="البحث في الموظفين..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <select
                value={departmentFilter}
                onChange={(e) => setDepartmentFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">جميع الأقسام</option>
                <option value="management">الإدارة</option>
                <option value="engineering">الهندسة</option>
                <option value="construction">البناء</option>
                <option value="finance">المالية</option>
                <option value="hr">الموارد البشرية</option>
              </select>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">جميع الحالات</option>
                <option value="active">نشط</option>
                <option value="inactive">غير نشط</option>
              </select>
            </div>
            <Button>
              إضافة موظف جديد
            </Button>
          </div>
        </Card>

        {/* قائمة الموظفين */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredEmployees.map((employee) => (
            <Card key={employee.id} className="hover:shadow-lg transition-shadow">
              <div className="space-y-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center ml-3">
                      <span className="text-white text-lg font-bold">
                        {employee.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{employee.name}</h3>
                      <p className="text-sm text-gray-600">{employee.position}</p>
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${getDepartmentColor(employee.department)}`}>
                      {getDepartmentText(employee.department)}
                    </span>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      employee.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {employee.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <span className="text-gray-600 w-16">البريد:</span>
                    <span className="font-medium">{employee.email}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <span className="text-gray-600 w-16">الهاتف:</span>
                    <span className="font-medium">{employee.phone}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <span className="text-gray-600 w-16">الراتب:</span>
                    <span className="font-medium">{employee.salary.toLocaleString()} ريال</span>
                  </div>
                </div>

                {/* المهارات */}
                {employee.skills.length > 0 && (
                  <div>
                    <p className="text-sm text-gray-600 mb-2">المهارات:</p>
                    <div className="flex flex-wrap gap-1">
                      {employee.skills.slice(0, 3).map((skill, index) => (
                        <span key={index} className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                          {skill}
                        </span>
                      ))}
                      {employee.skills.length > 3 && (
                        <span className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                          +{employee.skills.length - 3}
                        </span>
                      )}
                    </div>
                  </div>
                )}

                {/* إحصائيات الموظف */}
                <div className="grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
                  <div className="text-center">
                    <div className="text-lg font-bold text-blue-600">
                      {employee.assignedProjects.length}
                    </div>
                    <div className="text-xs text-gray-600">إجمالي المشاريع</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600">
                      {getActiveProjectsCount(employee.id)}
                    </div>
                    <div className="text-xs text-gray-600">مشاريع نشطة</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-purple-600">
                      {getYearsOfService(employee.hireDate)}
                    </div>
                    <div className="text-xs text-gray-600">سنوات الخدمة</div>
                  </div>
                </div>

                {/* تاريخ التوظيف */}
                <div className="text-xs text-gray-500">
                  تاريخ التوظيف: {employee.hireDate.toLocaleDateString('ar-SA')}
                </div>

                {/* أزرار الإجراءات */}
                <div className="flex gap-2 pt-4 border-t border-gray-200">
                  <Button size="sm" className="flex-1">
                    عرض التفاصيل
                  </Button>
                  <Button variant="outline" size="sm">
                    تعديل
                  </Button>
                  <Button variant="outline" size="sm">
                    📞
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {filteredEmployees.length === 0 && (
          <Card>
            <div className="text-center py-12">
              <div className="text-6xl mb-4">👷</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا يوجد موظفين</h3>
              <p className="text-gray-600 mb-4">لم يتم العثور على موظفين يطابقون معايير البحث</p>
              <Button>
                إضافة موظف جديد
              </Button>
            </div>
          </Card>
        )}
      </div>
    </Layout>
  );
}
