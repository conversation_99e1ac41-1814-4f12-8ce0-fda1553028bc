'use client';

import React, { useState } from 'react';
import { Layout } from '@/components/layout/Layout';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { mockMaterials } from '@/lib/data';
import { Material } from '@/types';

export default function MaterialsPage() {
  const [materials] = useState<Material[]>(mockMaterials);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [stockFilter, setStockFilter] = useState<string>('all');

  const filteredMaterials = materials.filter(material => {
    const matchesSearch = material.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         material.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         material.supplier.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || material.category === categoryFilter;
    const matchesStock = stockFilter === 'all' || 
                        (stockFilter === 'low' && material.currentStock <= material.minStock) ||
                        (stockFilter === 'normal' && material.currentStock > material.minStock);
    return matchesSearch && matchesCategory && matchesStock;
  });

  const getStockStatus = (material: Material) => {
    if (material.currentStock <= material.minStock) {
      return { status: 'low', text: 'مخزون منخفض', color: 'bg-red-100 text-red-800' };
    } else if (material.currentStock <= material.minStock * 1.5) {
      return { status: 'medium', text: 'مخزون متوسط', color: 'bg-yellow-100 text-yellow-800' };
    } else {
      return { status: 'good', text: 'مخزون جيد', color: 'bg-green-100 text-green-800' };
    }
  };

  const getStockPercentage = (material: Material) => {
    const maxStock = material.minStock * 3; // افتراض أن الحد الأقصى هو 3 أضعاف الحد الأدنى
    return Math.min((material.currentStock / maxStock) * 100, 100);
  };

  const getTotalValue = (material: Material) => {
    return material.currentStock * material.unitPrice;
  };

  const categories = [...new Set(materials.map(m => m.category))];

  return (
    <Layout title="إدارة المواد والمخزون">
      <div className="space-y-6">
        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="text-center">
            <div className="text-2xl font-bold text-blue-600">{materials.length}</div>
            <div className="text-sm text-gray-600">إجمالي المواد</div>
          </Card>
          <Card className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {materials.filter(m => m.currentStock <= m.minStock).length}
            </div>
            <div className="text-sm text-gray-600">مواد منخفضة المخزون</div>
          </Card>
          <Card className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {materials.reduce((sum, m) => sum + getTotalValue(m), 0).toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">قيمة المخزون (ريال)</div>
          </Card>
          <Card className="text-center">
            <div className="text-2xl font-bold text-purple-600">{categories.length}</div>
            <div className="text-sm text-gray-600">فئات المواد</div>
          </Card>
        </div>

        {/* شريط البحث والفلاتر */}
        <Card>
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex flex-col md:flex-row gap-4 flex-1">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="البحث في المواد..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">جميع الفئات</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
              <select
                value={stockFilter}
                onChange={(e) => setStockFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">جميع المخزون</option>
                <option value="low">مخزون منخفض</option>
                <option value="normal">مخزون طبيعي</option>
              </select>
            </div>
            <Button>
              إضافة مادة جديدة
            </Button>
          </div>
        </Card>

        {/* قائمة المواد */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredMaterials.map((material) => {
            const stockStatus = getStockStatus(material);
            const stockPercentage = getStockPercentage(material);
            
            return (
              <Card key={material.id} className="hover:shadow-lg transition-shadow">
                <div className="space-y-4">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{material.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{material.description}</p>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${stockStatus.color}`}>
                      {stockStatus.text}
                    </span>
                  </div>

                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-sm font-medium text-gray-900">{material.category}</p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">المخزون الحالي:</span>
                      <span className="font-medium">{material.currentStock} {material.unit}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">الحد الأدنى:</span>
                      <span className="font-medium">{material.minStock} {material.unit}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">سعر الوحدة:</span>
                      <span className="font-medium">{material.unitPrice} ريال</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">القيمة الإجمالية:</span>
                      <span className="font-medium">{getTotalValue(material).toLocaleString()} ريال</span>
                    </div>
                  </div>

                  {/* شريط المخزون */}
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-gray-600">مستوى المخزون</span>
                      <span className="font-medium">{stockPercentage.toFixed(0)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-300 ${
                          stockStatus.status === 'low' ? 'bg-red-500' :
                          stockStatus.status === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${stockPercentage}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* معلومات المورد */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">المورد:</span>
                      <span className="font-medium">{material.supplier}</span>
                    </div>
                    {material.lastPurchaseDate && (
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">آخر شراء:</span>
                        <span className="font-medium">{material.lastPurchaseDate.toLocaleDateString('ar-SA')}</span>
                      </div>
                    )}
                  </div>

                  {/* أزرار الإجراءات */}
                  <div className="flex gap-2 pt-4 border-t border-gray-200">
                    <Button size="sm" className="flex-1">
                      تحديث المخزون
                    </Button>
                    <Button variant="outline" size="sm">
                      تعديل
                    </Button>
                    <Button variant="outline" size="sm">
                      طلب شراء
                    </Button>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>

        {filteredMaterials.length === 0 && (
          <Card>
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📦</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مواد</h3>
              <p className="text-gray-600 mb-4">لم يتم العثور على مواد تطابق معايير البحث</p>
              <Button>
                إضافة مادة جديدة
              </Button>
            </div>
          </Card>
        )}
      </div>
    </Layout>
  );
}
