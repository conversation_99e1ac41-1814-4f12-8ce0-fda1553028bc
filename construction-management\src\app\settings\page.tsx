'use client';

import React, { useState } from 'react';
import { Layout } from '@/components/layout/Layout';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('company');
  const [companyInfo, setCompanyInfo] = useState({
    name: 'مؤسسة البناء المتقدم',
    email: '<EMAIL>',
    phone: '+966501234567',
    address: 'الرياض - حي النرجس - شارع الملك فهد',
    taxNumber: '*********',
    commercialRegister: 'CR-*********',
  });

  const [userSettings, setUserSettings] = useState({
    language: 'ar',
    theme: 'light',
    notifications: true,
    emailNotifications: true,
    smsNotifications: false,
  });

  const [systemSettings, setSystemSettings] = useState({
    currency: 'SAR',
    dateFormat: 'dd/mm/yyyy',
    timeFormat: '24h',
    backupFrequency: 'daily',
    autoSave: true,
  });

  const tabs = [
    { id: 'company', name: 'معلومات الشركة', icon: '🏢' },
    { id: 'user', name: 'إعدادات المستخدم', icon: '👤' },
    { id: 'system', name: 'إعدادات النظام', icon: '⚙️' },
    { id: 'security', name: 'الأمان', icon: '🔒' },
    { id: 'backup', name: 'النسخ الاحتياطي', icon: '💾' },
  ];

  const handleCompanyInfoChange = (field: string, value: string) => {
    setCompanyInfo(prev => ({ ...prev, [field]: value }));
  };

  const handleUserSettingsChange = (field: string, value: any) => {
    setUserSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleSystemSettingsChange = (field: string, value: any) => {
    setSystemSettings(prev => ({ ...prev, [field]: value }));
  };

  const renderCompanySettings = () => (
    <div className="space-y-6">
      <Card title="معلومات الشركة الأساسية">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              اسم الشركة
            </label>
            <input
              type="text"
              value={companyInfo.name}
              onChange={(e) => handleCompanyInfoChange('name', e.target.value)}
              className="input-field"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              البريد الإلكتروني
            </label>
            <input
              type="email"
              value={companyInfo.email}
              onChange={(e) => handleCompanyInfoChange('email', e.target.value)}
              className="input-field"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              رقم الهاتف
            </label>
            <input
              type="tel"
              value={companyInfo.phone}
              onChange={(e) => handleCompanyInfoChange('phone', e.target.value)}
              className="input-field"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الرقم الضريبي
            </label>
            <input
              type="text"
              value={companyInfo.taxNumber}
              onChange={(e) => handleCompanyInfoChange('taxNumber', e.target.value)}
              className="input-field"
            />
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              العنوان
            </label>
            <textarea
              value={companyInfo.address}
              onChange={(e) => handleCompanyInfoChange('address', e.target.value)}
              rows={3}
              className="input-field"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              رقم السجل التجاري
            </label>
            <input
              type="text"
              value={companyInfo.commercialRegister}
              onChange={(e) => handleCompanyInfoChange('commercialRegister', e.target.value)}
              className="input-field"
            />
          </div>
        </div>
      </Card>
    </div>
  );

  const renderUserSettings = () => (
    <div className="space-y-6">
      <Card title="التفضيلات الشخصية">
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              اللغة
            </label>
            <select
              value={userSettings.language}
              onChange={(e) => handleUserSettingsChange('language', e.target.value)}
              className="input-field"
            >
              <option value="ar">العربية</option>
              <option value="en">English</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              المظهر
            </label>
            <select
              value={userSettings.theme}
              onChange={(e) => handleUserSettingsChange('theme', e.target.value)}
              className="input-field"
            >
              <option value="light">فاتح</option>
              <option value="dark">داكن</option>
              <option value="auto">تلقائي</option>
            </select>
          </div>
        </div>
      </Card>

      <Card title="إعدادات التنبيهات">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">التنبيهات العامة</h4>
              <p className="text-sm text-gray-600">تلقي تنبيهات حول النشاطات المهمة</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={userSettings.notifications}
                onChange={(e) => handleUserSettingsChange('notifications', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">تنبيهات البريد الإلكتروني</h4>
              <p className="text-sm text-gray-600">تلقي تنبيهات عبر البريد الإلكتروني</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={userSettings.emailNotifications}
                onChange={(e) => handleUserSettingsChange('emailNotifications', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">تنبيهات الرسائل النصية</h4>
              <p className="text-sm text-gray-600">تلقي تنبيهات عبر الرسائل النصية</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={userSettings.smsNotifications}
                onChange={(e) => handleUserSettingsChange('smsNotifications', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </Card>
    </div>
  );

  const renderSystemSettings = () => (
    <div className="space-y-6">
      <Card title="إعدادات النظام العامة">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              العملة
            </label>
            <select
              value={systemSettings.currency}
              onChange={(e) => handleSystemSettingsChange('currency', e.target.value)}
              className="input-field"
            >
              <option value="SAR">ريال سعودي (SAR)</option>
              <option value="USD">دولار أمريكي (USD)</option>
              <option value="EUR">يورو (EUR)</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              تنسيق التاريخ
            </label>
            <select
              value={systemSettings.dateFormat}
              onChange={(e) => handleSystemSettingsChange('dateFormat', e.target.value)}
              className="input-field"
            >
              <option value="dd/mm/yyyy">يوم/شهر/سنة</option>
              <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
              <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              تنسيق الوقت
            </label>
            <select
              value={systemSettings.timeFormat}
              onChange={(e) => handleSystemSettingsChange('timeFormat', e.target.value)}
              className="input-field"
            >
              <option value="24h">24 ساعة</option>
              <option value="12h">12 ساعة</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              تكرار النسخ الاحتياطي
            </label>
            <select
              value={systemSettings.backupFrequency}
              onChange={(e) => handleSystemSettingsChange('backupFrequency', e.target.value)}
              className="input-field"
            >
              <option value="daily">يومياً</option>
              <option value="weekly">أسبوعياً</option>
              <option value="monthly">شهرياً</option>
            </select>
          </div>
        </div>
        <div className="mt-6">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">الحفظ التلقائي</h4>
              <p className="text-sm text-gray-600">حفظ التغييرات تلقائياً أثناء العمل</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={systemSettings.autoSave}
                onChange={(e) => handleSystemSettingsChange('autoSave', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </Card>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <Card title="إعدادات الأمان">
        <div className="space-y-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-4">تغيير كلمة المرور</h4>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  كلمة المرور الحالية
                </label>
                <input
                  type="password"
                  className="input-field"
                  placeholder="أدخل كلمة المرور الحالية"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  كلمة المرور الجديدة
                </label>
                <input
                  type="password"
                  className="input-field"
                  placeholder="أدخل كلمة المرور الجديدة"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تأكيد كلمة المرور الجديدة
                </label>
                <input
                  type="password"
                  className="input-field"
                  placeholder="أعد إدخال كلمة المرور الجديدة"
                />
              </div>
              <Button>
                تحديث كلمة المرور
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );

  const renderBackupSettings = () => (
    <div className="space-y-6">
      <Card title="النسخ الاحتياطي">
        <div className="space-y-6">
          <div className="text-center">
            <div className="text-4xl mb-4">💾</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">النسخ الاحتياطي للبيانات</h3>
            <p className="text-gray-600 mb-6">احتفظ بنسخة احتياطية من بياناتك لضمان الأمان</p>
            <div className="flex gap-4 justify-center">
              <Button>
                إنشاء نسخة احتياطية الآن
              </Button>
              <Button variant="outline">
                استعادة من نسخة احتياطية
              </Button>
            </div>
          </div>
          <div className="border-t pt-6">
            <h4 className="font-medium text-gray-900 mb-4">النسخ الاحتياطية الحديثة</h4>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                <div>
                  <div className="font-medium">نسخة احتياطية تلقائية</div>
                  <div className="text-sm text-gray-600">اليوم - 02:00 ص</div>
                </div>
                <Button variant="outline" size="sm">
                  تحميل
                </Button>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                <div>
                  <div className="font-medium">نسخة احتياطية يدوية</div>
                  <div className="text-sm text-gray-600">أمس - 05:30 م</div>
                </div>
                <Button variant="outline" size="sm">
                  تحميل
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'company': return renderCompanySettings();
      case 'user': return renderUserSettings();
      case 'system': return renderSystemSettings();
      case 'security': return renderSecuritySettings();
      case 'backup': return renderBackupSettings();
      default: return renderCompanySettings();
    }
  };

  return (
    <Layout title="الإعدادات">
      <div className="space-y-6">
        {/* تبويبات الإعدادات */}
        <Card>
          <div className="flex flex-wrap gap-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span className="ml-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </div>
        </Card>

        {/* محتوى التبويب */}
        {renderTabContent()}

        {/* أزرار الحفظ */}
        <Card>
          <div className="flex gap-4 justify-end">
            <Button variant="outline">
              إلغاء
            </Button>
            <Button>
              حفظ التغييرات
            </Button>
          </div>
        </Card>
      </div>
    </Layout>
  );
}
