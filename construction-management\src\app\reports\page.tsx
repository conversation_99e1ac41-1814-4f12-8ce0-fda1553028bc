'use client';

import React, { useState } from 'react';
import { Layout } from '@/components/layout/Layout';
import { Card, StatCard } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { mockProjects, mockClients, mockEmployees, mockMaterials, mockTransactions } from '@/lib/data';

export default function ReportsPage() {
  const [selectedReport, setSelectedReport] = useState<string>('overview');

  // حساب الإحصائيات
  const totalProjects = mockProjects.length;
  const activeProjects = mockProjects.filter(p => p.status === 'in-progress').length;
  const completedProjects = mockProjects.filter(p => p.status === 'completed').length;
  const totalRevenue = mockTransactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0);
  const totalExpenses = mockTransactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0);
  const netProfit = totalRevenue - totalExpenses;

  // تقارير المشاريع
  const projectsByType = mockProjects.reduce((acc, project) => {
    acc[project.projectType] = (acc[project.projectType] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const projectsByStatus = mockProjects.reduce((acc, project) => {
    acc[project.status] = (acc[project.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // تقارير الموظفين
  const employeesByDepartment = mockEmployees.reduce((acc, employee) => {
    acc[employee.department] = (acc[employee.department] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // تقارير المواد
  const lowStockMaterials = mockMaterials.filter(m => m.currentStock <= m.minStock);
  const totalMaterialsValue = mockMaterials.reduce((sum, m) => sum + (m.currentStock * m.unitPrice), 0);

  const getProjectTypeText = (type: string) => {
    switch (type) {
      case 'residential': return 'سكني';
      case 'commercial': return 'تجاري';
      case 'industrial': return 'صناعي';
      case 'infrastructure': return 'بنية تحتية';
      default: return type;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'planned': return 'مخطط';
      case 'in-progress': return 'قيد التنفيذ';
      case 'completed': return 'مكتمل';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const getDepartmentText = (department: string) => {
    switch (department) {
      case 'management': return 'الإدارة';
      case 'engineering': return 'الهندسة';
      case 'construction': return 'البناء';
      case 'finance': return 'المالية';
      case 'hr': return 'الموارد البشرية';
      default: return department;
    }
  };

  const reportTypes = [
    { id: 'overview', name: 'نظرة عامة', icon: '📊' },
    { id: 'projects', name: 'تقارير المشاريع', icon: '🏗️' },
    { id: 'financial', name: 'التقارير المالية', icon: '💰' },
    { id: 'employees', name: 'تقارير الموظفين', icon: '👷' },
    { id: 'materials', name: 'تقارير المواد', icon: '📦' },
  ];

  const renderOverviewReport = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatCard
          title="إجمالي المشاريع"
          value={totalProjects}
          icon="🏗️"
        />
        <StatCard
          title="إجمالي العملاء"
          value={mockClients.length}
          icon="👥"
        />
        <StatCard
          title="إجمالي الموظفين"
          value={mockEmployees.length}
          icon="👷"
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card title="حالة المشاريع">
          <div className="space-y-3">
            {Object.entries(projectsByStatus).map(([status, count]) => (
              <div key={status} className="flex justify-between items-center">
                <span className="text-gray-700">{getStatusText(status)}</span>
                <span className="font-bold text-blue-600">{count}</span>
              </div>
            ))}
          </div>
        </Card>
        
        <Card title="الأداء المالي">
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-700">إجمالي الإيرادات</span>
              <span className="font-bold text-green-600">{totalRevenue.toLocaleString()} ريال</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-700">إجمالي المصروفات</span>
              <span className="font-bold text-red-600">{totalExpenses.toLocaleString()} ريال</span>
            </div>
            <div className="flex justify-between items-center border-t pt-2">
              <span className="text-gray-700 font-medium">صافي الربح</span>
              <span className="font-bold text-blue-600">{netProfit.toLocaleString()} ريال</span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );

  const renderProjectsReport = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card title="المشاريع حسب النوع">
          <div className="space-y-3">
            {Object.entries(projectsByType).map(([type, count]) => (
              <div key={type} className="flex justify-between items-center">
                <span className="text-gray-700">{getProjectTypeText(type)}</span>
                <span className="font-bold text-blue-600">{count}</span>
              </div>
            ))}
          </div>
        </Card>
        
        <Card title="المشاريع حسب الحالة">
          <div className="space-y-3">
            {Object.entries(projectsByStatus).map(([status, count]) => (
              <div key={status} className="flex justify-between items-center">
                <span className="text-gray-700">{getStatusText(status)}</span>
                <span className="font-bold text-blue-600">{count}</span>
              </div>
            ))}
          </div>
        </Card>
      </div>
      
      <Card title="تفاصيل المشاريع">
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-right py-2">اسم المشروع</th>
                <th className="text-right py-2">النوع</th>
                <th className="text-right py-2">الحالة</th>
                <th className="text-right py-2">التكلفة المقدرة</th>
                <th className="text-right py-2">نسبة الإنجاز</th>
              </tr>
            </thead>
            <tbody>
              {mockProjects.map((project) => (
                <tr key={project.id} className="border-b">
                  <td className="py-2">{project.name}</td>
                  <td className="py-2">{getProjectTypeText(project.projectType)}</td>
                  <td className="py-2">{getStatusText(project.status)}</td>
                  <td className="py-2">{project.estimatedCost.toLocaleString()} ريال</td>
                  <td className="py-2">{project.progress}%</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );

  const renderFinancialReport = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatCard
          title="إجمالي الإيرادات"
          value={`${totalRevenue.toLocaleString()} ريال`}
          icon="💰"
        />
        <StatCard
          title="إجمالي المصروفات"
          value={`${totalExpenses.toLocaleString()} ريال`}
          icon="💸"
        />
        <StatCard
          title="صافي الربح"
          value={`${netProfit.toLocaleString()} ريال`}
          icon="📈"
        />
      </div>
      
      <Card title="المعاملات المالية الحديثة">
        <div className="space-y-3">
          {mockTransactions.slice(0, 10).map((transaction) => (
            <div key={transaction.id} className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <div>
                <div className="font-medium">{transaction.description}</div>
                <div className="text-sm text-gray-600">{transaction.date.toLocaleDateString('ar-SA')}</div>
              </div>
              <div className={`font-bold ${transaction.type === 'income' ? 'text-green-600' : 'text-red-600'}`}>
                {transaction.type === 'income' ? '+' : '-'}{transaction.amount.toLocaleString()} ريال
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );

  const renderEmployeesReport = () => (
    <div className="space-y-6">
      <Card title="الموظفين حسب القسم">
        <div className="space-y-3">
          {Object.entries(employeesByDepartment).map(([department, count]) => (
            <div key={department} className="flex justify-between items-center">
              <span className="text-gray-700">{getDepartmentText(department)}</span>
              <span className="font-bold text-blue-600">{count}</span>
            </div>
          ))}
        </div>
      </Card>
      
      <Card title="قائمة الموظفين">
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-right py-2">الاسم</th>
                <th className="text-right py-2">المنصب</th>
                <th className="text-right py-2">القسم</th>
                <th className="text-right py-2">الراتب</th>
                <th className="text-right py-2">الحالة</th>
              </tr>
            </thead>
            <tbody>
              {mockEmployees.map((employee) => (
                <tr key={employee.id} className="border-b">
                  <td className="py-2">{employee.name}</td>
                  <td className="py-2">{employee.position}</td>
                  <td className="py-2">{getDepartmentText(employee.department)}</td>
                  <td className="py-2">{employee.salary.toLocaleString()} ريال</td>
                  <td className="py-2">{employee.isActive ? 'نشط' : 'غير نشط'}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );

  const renderMaterialsReport = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatCard
          title="إجمالي المواد"
          value={mockMaterials.length}
          icon="📦"
        />
        <StatCard
          title="مواد منخفضة المخزون"
          value={lowStockMaterials.length}
          icon="⚠️"
        />
        <StatCard
          title="قيمة المخزون"
          value={`${totalMaterialsValue.toLocaleString()} ريال`}
          icon="💰"
        />
      </div>
      
      <Card title="المواد منخفضة المخزون">
        <div className="space-y-3">
          {lowStockMaterials.map((material) => (
            <div key={material.id} className="flex justify-between items-center p-3 bg-red-50 rounded">
              <div>
                <div className="font-medium">{material.name}</div>
                <div className="text-sm text-gray-600">{material.category}</div>
              </div>
              <div className="text-right">
                <div className="font-bold text-red-600">{material.currentStock} {material.unit}</div>
                <div className="text-sm text-gray-600">الحد الأدنى: {material.minStock}</div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );

  const renderReport = () => {
    switch (selectedReport) {
      case 'overview': return renderOverviewReport();
      case 'projects': return renderProjectsReport();
      case 'financial': return renderFinancialReport();
      case 'employees': return renderEmployeesReport();
      case 'materials': return renderMaterialsReport();
      default: return renderOverviewReport();
    }
  };

  return (
    <Layout title="التقارير">
      <div className="space-y-6">
        {/* أنواع التقارير */}
        <Card>
          <div className="flex flex-wrap gap-2">
            {reportTypes.map((type) => (
              <button
                key={type.id}
                onClick={() => setSelectedReport(type.id)}
                className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                  selectedReport === type.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span className="ml-2">{type.icon}</span>
                {type.name}
              </button>
            ))}
          </div>
        </Card>

        {/* محتوى التقرير */}
        {renderReport()}

        {/* أزرار الإجراءات */}
        <Card>
          <div className="flex gap-4 justify-center">
            <Button>
              طباعة التقرير
            </Button>
            <Button variant="outline">
              تصدير PDF
            </Button>
            <Button variant="outline">
              تصدير Excel
            </Button>
          </div>
        </Card>
      </div>
    </Layout>
  );
}
