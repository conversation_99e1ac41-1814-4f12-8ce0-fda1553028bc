'use client';

import React, { useState } from 'react';
import { Layout } from '@/components/layout/Layout';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { mockProjects, mockClients } from '@/lib/data';
import { Project } from '@/types';

export default function ProjectsPage() {
  const [projects] = useState<Project[]>(mockProjects);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || project.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusText = (status: string) => {
    switch (status) {
      case 'planned': return 'مخطط';
      case 'in-progress': return 'قيد التنفيذ';
      case 'completed': return 'مكتمل';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planned': return 'bg-yellow-100 text-yellow-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getProjectTypeText = (type: string) => {
    switch (type) {
      case 'residential': return 'سكني';
      case 'commercial': return 'تجاري';
      case 'industrial': return 'صناعي';
      case 'infrastructure': return 'بنية تحتية';
      default: return type;
    }
  };

  const getClientName = (clientId: string) => {
    const client = mockClients.find(c => c.id === clientId);
    return client ? client.name : 'غير محدد';
  };

  return (
    <Layout title="إدارة المشاريع">
      <div className="space-y-6">
        {/* شريط البحث والفلاتر */}
        <Card>
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex flex-col md:flex-row gap-4 flex-1">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="البحث في المشاريع..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">جميع الحالات</option>
                <option value="planned">مخطط</option>
                <option value="in-progress">قيد التنفيذ</option>
                <option value="completed">مكتمل</option>
                <option value="cancelled">ملغي</option>
              </select>
            </div>
            <Button>
              إضافة مشروع جديد
            </Button>
          </div>
        </Card>

        {/* قائمة المشاريع */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredProjects.map((project) => (
            <Card key={project.id} className="hover:shadow-lg transition-shadow">
              <div className="space-y-4">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{project.name}</h3>
                    <p className="text-sm text-gray-600 mt-1">{project.location}</p>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(project.status)}`}>
                    {getStatusText(project.status)}
                  </span>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">العميل:</span>
                    <span className="font-medium">{getClientName(project.clientId)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">نوع المشروع:</span>
                    <span className="font-medium">{getProjectTypeText(project.projectType)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">التكلفة المقدرة:</span>
                    <span className="font-medium">{project.estimatedCost.toLocaleString()} ريال</span>
                  </div>
                </div>

                {/* شريط التقدم */}
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600">نسبة الإنجاز</span>
                    <span className="font-medium">{project.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                      style={{ width: `${project.progress}%` }}
                    ></div>
                  </div>
                </div>

                {/* تواريخ المشروع */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600 block">تاريخ البداية</span>
                    <span className="font-medium">{project.startDate.toLocaleDateString('ar-SA')}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 block">تاريخ الانتهاء</span>
                    <span className="font-medium">{project.endDate.toLocaleDateString('ar-SA')}</span>
                  </div>
                </div>

                {/* أزرار الإجراءات */}
                <div className="flex gap-2 pt-4 border-t border-gray-200">
                  <Button size="sm" className="flex-1">
                    عرض التفاصيل
                  </Button>
                  <Button variant="outline" size="sm">
                    تعديل
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {filteredProjects.length === 0 && (
          <Card>
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🏗️</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مشاريع</h3>
              <p className="text-gray-600 mb-4">لم يتم العثور على مشاريع تطابق معايير البحث</p>
              <Button>
                إضافة مشروع جديد
              </Button>
            </div>
          </Card>
        )}
      </div>
    </Layout>
  );
}
