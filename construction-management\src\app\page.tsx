'use client';

import React from 'react';
import { Layout } from '@/components/layout/Layout';
import { StatCard, Card } from '@/components/ui/Card';
import { mockDashboardStats, mockProjects, mockTransactions } from '@/lib/data';

export default function Home() {
  const stats = mockDashboardStats;
  const recentProjects = mockProjects.slice(0, 3);
  const recentTransactions = mockTransactions.slice(0, 5);

  return (
    <Layout title="لوحة التحكم">
      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="إجمالي المشاريع"
          value={stats.totalProjects}
          icon="🏗️"
          trend={{ value: 12, isPositive: true }}
        />
        <StatCard
          title="المشاريع النشطة"
          value={stats.activeProjects}
          icon="⚡"
          trend={{ value: 8, isPositive: true }}
        />
        <StatCard
          title="إجمالي الإيرادات"
          value={`${stats.totalRevenue.toLocaleString()} ريال`}
          icon="💰"
          trend={{ value: 15, isPositive: true }}
        />
        <StatCard
          title="صافي الربح"
          value={`${stats.netProfit.toLocaleString()} ريال`}
          icon="📈"
          trend={{ value: 22, isPositive: true }}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* المشاريع الحديثة */}
        <Card title="المشاريع الحديثة" subtitle="آخر المشاريع المضافة">
          <div className="space-y-4">
            {recentProjects.map((project) => (
              <div key={project.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">{project.name}</h4>
                  <p className="text-sm text-gray-600">{project.location}</p>
                  <div className="flex items-center mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-2 ml-3">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600">{project.progress}%</span>
                  </div>
                </div>
                <div className="text-left">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    project.status === 'completed' ? 'bg-green-100 text-green-800' :
                    project.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    {project.status === 'completed' ? 'مكتمل' :
                     project.status === 'in-progress' ? 'قيد التنفيذ' : 'مخطط'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* المعاملات المالية الحديثة */}
        <Card title="المعاملات المالية" subtitle="آخر المعاملات المالية">
          <div className="space-y-4">
            {recentTransactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">{transaction.description}</h4>
                  <p className="text-sm text-gray-600">{transaction.category}</p>
                  <p className="text-xs text-gray-500">{transaction.date.toLocaleDateString('ar-SA')}</p>
                </div>
                <div className="text-left">
                  <span className={`font-bold ${
                    transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {transaction.type === 'income' ? '+' : '-'}{transaction.amount.toLocaleString()} ريال
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* إحصائيات إضافية */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
        <StatCard
          title="إجمالي العملاء"
          value={stats.totalClients}
          icon="👥"
        />
        <StatCard
          title="إجمالي الموظفين"
          value={stats.totalEmployees}
          icon="👷"
        />
        <StatCard
          title="المواد منخفضة المخزون"
          value={stats.lowStockMaterials}
          icon="📦"
        />
      </div>
    </Layout>
  );
}
