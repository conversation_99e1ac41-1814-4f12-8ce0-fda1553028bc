// بيانات تجريبية لنظام إدارة مؤسسة مقاولات البناء
import { Project, Client, Employee, Material, Transaction, Invoice, DashboardStats } from '@/types';

export const mockProjects: Project[] = [
  {
    id: '1',
    name: 'مشروع فيلا العائلة السعيدة',
    description: 'بناء فيلا سكنية من طابقين مع حديقة',
    clientId: '1',
    startDate: new Date('2024-01-15'),
    endDate: new Date('2024-06-15'),
    estimatedCost: 500000,
    actualCost: 480000,
    status: 'completed',
    location: 'الرياض - حي النرجس',
    projectType: 'residential',
    progress: 100,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-06-15'),
  },
  {
    id: '2',
    name: 'مجمع تجاري الأزهار',
    description: 'بناء مجمع تجاري يحتوي على 20 محل تجاري',
    clientId: '2',
    startDate: new Date('2024-03-01'),
    endDate: new Date('2024-12-01'),
    estimatedCost: 2000000,
    actualCost: 1800000,
    status: 'in-progress',
    location: 'جدة - حي الصفا',
    projectType: 'commercial',
    progress: 75,
    createdAt: new Date('2024-02-15'),
    updatedAt: new Date('2024-11-01'),
  },
  {
    id: '3',
    name: 'مصنع الإنتاج الصناعي',
    description: 'بناء مصنع للإنتاج الصناعي مع المرافق',
    clientId: '3',
    startDate: new Date('2024-05-01'),
    endDate: new Date('2025-02-01'),
    estimatedCost: 5000000,
    actualCost: 2500000,
    status: 'in-progress',
    location: 'الدمام - المنطقة الصناعية',
    projectType: 'industrial',
    progress: 45,
    createdAt: new Date('2024-04-15'),
    updatedAt: new Date('2024-11-15'),
  },
];

export const mockClients: Client[] = [
  {
    id: '1',
    name: 'أحمد محمد السعيد',
    email: '<EMAIL>',
    phone: '+966501234567',
    address: 'الرياض - حي النرجس - شارع الملك فهد',
    clientType: 'individual',
    projects: ['1'],
    createdAt: new Date('2023-12-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    name: 'شركة الأزهار للاستثمار',
    email: '<EMAIL>',
    phone: '+966502345678',
    address: 'جدة - حي الصفا - طريق الملك عبدالعزيز',
    company: 'شركة الأزهار للاستثمار',
    clientType: 'company',
    projects: ['2'],
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-02-15'),
  },
  {
    id: '3',
    name: 'مؤسسة الإنتاج الصناعي',
    email: '<EMAIL>',
    phone: '+966503456789',
    address: 'الدمام - المنطقة الصناعية الثانية',
    company: 'مؤسسة الإنتاج الصناعي',
    clientType: 'company',
    projects: ['3'],
    createdAt: new Date('2024-03-01'),
    updatedAt: new Date('2024-04-15'),
  },
];

export const mockEmployees: Employee[] = [
  {
    id: '1',
    name: 'محمد عبدالله الأحمد',
    email: '<EMAIL>',
    phone: '+966504567890',
    position: 'مدير المشاريع',
    department: 'management',
    salary: 15000,
    hireDate: new Date('2020-01-15'),
    isActive: true,
    skills: ['إدارة المشاريع', 'التخطيط', 'القيادة'],
    assignedProjects: ['1', '2'],
    createdAt: new Date('2020-01-15'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    name: 'سارة أحمد المطيري',
    email: '<EMAIL>',
    phone: '+966505678901',
    position: 'مهندسة مدنية',
    department: 'engineering',
    salary: 12000,
    hireDate: new Date('2021-03-01'),
    isActive: true,
    skills: ['التصميم الإنشائي', 'AutoCAD', 'إدارة الموقع'],
    assignedProjects: ['2', '3'],
    createdAt: new Date('2021-03-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '3',
    name: 'خالد سعد الغامدي',
    email: '<EMAIL>',
    phone: '+966506789012',
    position: 'مشرف البناء',
    department: 'construction',
    salary: 8000,
    hireDate: new Date('2019-06-15'),
    isActive: true,
    skills: ['الإشراف على البناء', 'السلامة', 'إدارة العمال'],
    assignedProjects: ['1', '3'],
    createdAt: new Date('2019-06-15'),
    updatedAt: new Date('2024-01-01'),
  },
];

export const mockMaterials: Material[] = [
  {
    id: '1',
    name: 'أسمنت بورتلاندي',
    description: 'أسمنت عالي الجودة للبناء',
    category: 'مواد البناء الأساسية',
    unit: 'كيس 50 كيلو',
    currentStock: 500,
    minStock: 100,
    unitPrice: 25,
    supplier: 'شركة الأسمنت السعودية',
    lastPurchaseDate: new Date('2024-10-15'),
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-10-15'),
  },
  {
    id: '2',
    name: 'حديد التسليح 12 مم',
    description: 'حديد تسليح عالي المقاومة',
    category: 'حديد التسليح',
    unit: 'طن',
    currentStock: 50,
    minStock: 20,
    unitPrice: 2500,
    supplier: 'مصنع الحديد والصلب',
    lastPurchaseDate: new Date('2024-11-01'),
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-11-01'),
  },
  {
    id: '3',
    name: 'بلوك خرساني 20x20x40',
    description: 'بلوك خرساني للجدران',
    category: 'مواد البناء',
    unit: 'قطعة',
    currentStock: 2000,
    minStock: 500,
    unitPrice: 3.5,
    supplier: 'مصنع البلوك الحديث',
    lastPurchaseDate: new Date('2024-10-20'),
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-10-20'),
  },
];

export const mockTransactions: Transaction[] = [
  {
    id: '1',
    type: 'income',
    amount: 250000,
    description: 'دفعة أولى من مشروع فيلا العائلة السعيدة',
    category: 'إيرادات المشاريع',
    projectId: '1',
    date: new Date('2024-01-15'),
    paymentMethod: 'bank_transfer',
    reference: 'INV-001',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
  },
  {
    id: '2',
    type: 'expense',
    amount: 50000,
    description: 'شراء مواد بناء للمشروع',
    category: 'مواد البناء',
    projectId: '1',
    date: new Date('2024-01-20'),
    paymentMethod: 'cash',
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20'),
  },
  {
    id: '3',
    type: 'income',
    amount: 500000,
    description: 'دفعة أولى من مجمع تجاري الأزهار',
    category: 'إيرادات المشاريع',
    projectId: '2',
    date: new Date('2024-03-01'),
    paymentMethod: 'check',
    reference: 'INV-002',
    createdAt: new Date('2024-03-01'),
    updatedAt: new Date('2024-03-01'),
  },
];

export const mockDashboardStats: DashboardStats = {
  totalProjects: 3,
  activeProjects: 2,
  completedProjects: 1,
  totalRevenue: 750000,
  totalExpenses: 50000,
  netProfit: 700000,
  totalClients: 3,
  totalEmployees: 3,
  lowStockMaterials: 0,
  overdueInvoices: 0,
};
