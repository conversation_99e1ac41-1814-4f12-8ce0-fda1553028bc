// أنواع البيانات الأساسية لنظام إدارة مؤسسة مقاولات البناء

export interface Project {
  id: string;
  name: string;
  description: string;
  clientId: string;
  startDate: Date;
  endDate: Date;
  estimatedCost: number;
  actualCost: number;
  status: 'planned' | 'in-progress' | 'completed' | 'cancelled';
  location: string;
  projectType: 'residential' | 'commercial' | 'industrial' | 'infrastructure';
  progress: number; // نسبة الإنجاز من 0 إلى 100
  createdAt: Date;
  updatedAt: Date;
}

export interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  company?: string;
  clientType: 'individual' | 'company';
  projects: string[]; // معرفات المشاريع
  createdAt: Date;
  updatedAt: Date;
}

export interface Employee {
  id: string;
  name: string;
  email: string;
  phone: string;
  position: string;
  department: 'management' | 'engineering' | 'construction' | 'finance' | 'hr';
  salary: number;
  hireDate: Date;
  isActive: boolean;
  skills: string[];
  assignedProjects: string[]; // معرفات المشاريع المكلف بها
  createdAt: Date;
  updatedAt: Date;
}

export interface Material {
  id: string;
  name: string;
  description: string;
  category: string;
  unit: string; // وحدة القياس (متر، كيلو، قطعة، إلخ)
  currentStock: number;
  minStock: number; // الحد الأدنى للمخزون
  unitPrice: number;
  supplier: string;
  lastPurchaseDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Transaction {
  id: string;
  type: 'income' | 'expense';
  amount: number;
  description: string;
  category: string;
  projectId?: string; // مرتبط بمشروع معين
  date: Date;
  paymentMethod: 'cash' | 'bank_transfer' | 'check' | 'credit_card';
  reference?: string; // رقم مرجعي
  createdAt: Date;
  updatedAt: Date;
}

export interface Invoice {
  id: string;
  projectId: string;
  clientId: string;
  invoiceNumber: string;
  amount: number;
  tax: number;
  totalAmount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
  issueDate: Date;
  dueDate: Date;
  paidDate?: Date;
  items: InvoiceItem[];
  createdAt: Date;
  updatedAt: Date;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface DashboardStats {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  totalClients: number;
  totalEmployees: number;
  lowStockMaterials: number;
  overdueInvoices: number;
}

export interface ProjectMaterial {
  projectId: string;
  materialId: string;
  quantityUsed: number;
  dateUsed: Date;
}

export interface ProjectEmployee {
  projectId: string;
  employeeId: string;
  role: string;
  assignedDate: Date;
  isActive: boolean;
}
