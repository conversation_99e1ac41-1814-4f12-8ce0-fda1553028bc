'use client';

import React, { useState } from 'react';
import { Layout } from '@/components/layout/Layout';
import { Card, StatCard } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { mockTransactions, mockProjects, mockClients } from '@/lib/data';
import { Transaction } from '@/types';

export default function FinancePage() {
  const [transactions] = useState<Transaction[]>(mockTransactions);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.category.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || transaction.type === typeFilter;
    
    let matchesDate = true;
    if (dateFilter !== 'all') {
      const now = new Date();
      const transactionDate = transaction.date;
      
      switch (dateFilter) {
        case 'today':
          matchesDate = transactionDate.toDateString() === now.toDateString();
          break;
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          matchesDate = transactionDate >= weekAgo;
          break;
        case 'month':
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          matchesDate = transactionDate >= monthAgo;
          break;
      }
    }
    
    return matchesSearch && matchesType && matchesDate;
  });

  const totalIncome = transactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);

  const totalExpenses = transactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);

  const netProfit = totalIncome - totalExpenses;

  const getTransactionIcon = (type: string) => {
    return type === 'income' ? '💰' : '💸';
  };

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'cash': return 'نقداً';
      case 'bank_transfer': return 'تحويل بنكي';
      case 'check': return 'شيك';
      case 'credit_card': return 'بطاقة ائتمان';
      default: return method;
    }
  };

  const getProjectName = (projectId?: string) => {
    if (!projectId) return 'غير محدد';
    const project = mockProjects.find(p => p.id === projectId);
    return project ? project.name : 'غير محدد';
  };

  const monthlyData = transactions.reduce((acc, transaction) => {
    const month = transaction.date.toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
    if (!acc[month]) {
      acc[month] = { income: 0, expenses: 0 };
    }
    if (transaction.type === 'income') {
      acc[month].income += transaction.amount;
    } else {
      acc[month].expenses += transaction.amount;
    }
    return acc;
  }, {} as Record<string, { income: number; expenses: number }>);

  return (
    <Layout title="إدارة المالية">
      <div className="space-y-6">
        {/* إحصائيات مالية سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <StatCard
            title="إجمالي الإيرادات"
            value={`${totalIncome.toLocaleString()} ريال`}
            icon="💰"
            trend={{ value: 15, isPositive: true }}
          />
          <StatCard
            title="إجمالي المصروفات"
            value={`${totalExpenses.toLocaleString()} ريال`}
            icon="💸"
            trend={{ value: 8, isPositive: false }}
          />
          <StatCard
            title="صافي الربح"
            value={`${netProfit.toLocaleString()} ريال`}
            icon="📈"
            trend={{ value: 22, isPositive: true }}
          />
          <StatCard
            title="عدد المعاملات"
            value={transactions.length}
            icon="📊"
          />
        </div>

        {/* الرسم البياني الشهري */}
        <Card title="الأداء المالي الشهري" subtitle="مقارنة الإيرادات والمصروفات">
          <div className="space-y-4">
            {Object.entries(monthlyData).map(([month, data]) => (
              <div key={month} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-gray-900">{month}</span>
                  <span className="text-sm text-gray-600">
                    صافي: {(data.income - data.expenses).toLocaleString()} ريال
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-green-600">الإيرادات</span>
                      <span className="font-medium">{data.income.toLocaleString()} ريال</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full" 
                        style={{ width: `${Math.min((data.income / Math.max(totalIncome, totalExpenses)) * 100, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-red-600">المصروفات</span>
                      <span className="font-medium">{data.expenses.toLocaleString()} ريال</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-red-500 h-2 rounded-full" 
                        style={{ width: `${Math.min((data.expenses / Math.max(totalIncome, totalExpenses)) * 100, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* شريط البحث والفلاتر */}
        <Card>
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex flex-col md:flex-row gap-4 flex-1">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="البحث في المعاملات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">جميع الأنواع</option>
                <option value="income">إيرادات</option>
                <option value="expense">مصروفات</option>
              </select>
              <select
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">جميع التواريخ</option>
                <option value="today">اليوم</option>
                <option value="week">هذا الأسبوع</option>
                <option value="month">هذا الشهر</option>
              </select>
            </div>
            <Button>
              إضافة معاملة جديدة
            </Button>
          </div>
        </Card>

        {/* قائمة المعاملات */}
        <Card title="المعاملات المالية" subtitle="جميع المعاملات المالية الحديثة">
          <div className="space-y-4">
            {filteredTransactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div className="flex items-center">
                  <div className="text-2xl ml-4">
                    {getTransactionIcon(transaction.type)}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{transaction.description}</h4>
                    <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                      <span>{transaction.category}</span>
                      <span>•</span>
                      <span>{getPaymentMethodText(transaction.paymentMethod)}</span>
                      {transaction.projectId && (
                        <>
                          <span>•</span>
                          <span>{getProjectName(transaction.projectId)}</span>
                        </>
                      )}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {transaction.date.toLocaleDateString('ar-SA')} - {transaction.date.toLocaleTimeString('ar-SA')}
                    </div>
                  </div>
                </div>
                <div className="text-left">
                  <div className={`text-lg font-bold ${
                    transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {transaction.type === 'income' ? '+' : '-'}{transaction.amount.toLocaleString()} ريال
                  </div>
                  {transaction.reference && (
                    <div className="text-xs text-gray-500">
                      المرجع: {transaction.reference}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </Card>

        {filteredTransactions.length === 0 && (
          <Card>
            <div className="text-center py-12">
              <div className="text-6xl mb-4">💰</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد معاملات</h3>
              <p className="text-gray-600 mb-4">لم يتم العثور على معاملات تطابق معايير البحث</p>
              <Button>
                إضافة معاملة جديدة
              </Button>
            </div>
          </Card>
        )}
      </div>
    </Layout>
  );
}
